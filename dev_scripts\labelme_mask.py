import numpy as np

from src.utils.labelme_to_mask import parse_labelme_to_mask, visualize_mask


def main():
    json_path = 'data/OCR/20250415/images/images_processed/20250415_102820052292_prep.json'

    mask = parse_labelme_to_mask(json_path)
    print(f'Full mask shape: {mask.shape}')
    print(f'Unique labels: {np.unique(mask)}')
    print(f'Label counts: {np.bincount(mask.flatten())}')
    print()

    total_pixels = mask.size
    unknown_count = np.sum(mask == 0)
    clean_count = np.sum(mask == 1)
    text_count = np.sum(mask == 2)

    print(f'Total pixels: {total_pixels:,}')
    print(f'Unknown (0): {unknown_count:,} ({unknown_count / total_pixels * 100:.1f}%)')
    print(f'Clean (1):   {clean_count:,} ({clean_count / total_pixels * 100:.1f}%)')
    print(f'Text (2):    {text_count:,} ({text_count / total_pixels * 100:.1f}%)')
    print()

    visualize_mask(mask, 'LabelMe Polygons to Training Labels')


if __name__ == '__main__':
    main()

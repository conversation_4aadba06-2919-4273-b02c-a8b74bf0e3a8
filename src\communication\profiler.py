import time
from pathlib import Path
from typing import Protocol

import cv2 as cv
from heliovision.camera.base import Fr<PERSON>, FrameQueue, OffThreadAsyncFrameQueue


class ProfilerProtocol(Protocol):
    async def start_run(self, *args, **kwargs) -> FrameQueue: ...

    def update_settings(self, *args, **kwargs) -> None: ...

    def trigger(self) -> None: ...


class MockProfiler(ProfilerProtocol):
    def __init__(self, data_folder: Path, delay: float = 0.0) -> None:
        self._frame_queue = OffThreadAsyncFrameQueue()
        self._data_folder = data_folder
        self._files = sorted(
            [f for f in data_folder.iterdir() if f.is_file() and f.suffix == '.png']
        )
        self._delay = delay

    async def start_run(self, *args, **kwargs) -> FrameQueue:
        return self._frame_queue

    def update_settings(self, *args, **kwargs) -> None:
        pass

    def trigger(self) -> None:
        if not self._files:
            raise RuntimeError('No more files to process')
        counter = 0
        while counter < len(self._files):
            file_path = self._files[counter]
            img = cv.imread(str(file_path))
            if img is None:
                raise RuntimeError(f'Failed to read image from {file_path}')
            frame = Frame()
            frame.image = img
            time.sleep(self._delay)
            self._frame_queue.put(frame)
            if counter == len(self._files) - 1:
                counter = 0

from typing import Any, Optional, Protocol, Type

from heliovision.communication.opcua import OPCUAClient

from src.communication.opcua_nodes import OPCUANodes


class OPCClientProtocol(Protocol):
    async def initialize(self) -> None: ...

    async def send(self, node: OPCUANodes, value: Any) -> None: ...

    async def receive(self, node: OPCUAN<PERSON>) -> Any: ...


class OPCClient:
    def __init__(
        self,
        server_url: str,
        node_descriptions: Type[OPCUANodes],
        namespace: Optional[str] = None,
    ) -> None:
        self._client = OPCUAClient(server_url, namespace_name=namespace)
        self._node_descriptions = node_descriptions
        self._nodes = {}

    async def initialize(self) -> None:
        """Initialize the OPC UA client."""
        await self._client.connect()
        self._nodes = {
            name: await self._client.get_node(node.node_id, node.data_type)
            for name, node in self._node_descriptions.get_all_nodes().items()
        }

    async def send(self, node: OPCUANodes, value: Any) -> None:
        """Send a value to a node."""
        node_name = node.name
        if node_name not in self._nodes:
            raise KeyError(f'Node {node_name} not found in the client')
        required_type = self._node_descriptions.get_node(node_name).data_type
        if isinstance(value, list):
            if not all(isinstance(v, required_type) for v in value):
                raise ValueError(f'Value type mismatch for node {node_name}')
        elif not isinstance(value, required_type):
            raise ValueError(f'Value type mismatch for node {node_name}')
        await self._nodes[node_name].set(value)

    async def receive(self, node: OPCUANodes) -> Any:
        """Receive a value from a node."""
        try:
            node_name = node.name
            if node_name not in self._nodes:
                raise KeyError(f'Node {node_name} not found in the client')
        except KeyError:
            raise ValueError(f'Node {node_name} not found in the client')
        return await self._nodes[node_name].get()

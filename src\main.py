import asyncio

import numpy as np
from heliovision.camera.base import <PERSON>ame<PERSON><PERSON><PERSON>
from loguru import logger

from src.analyzer import AnalyzerProtocol
from src.communication.botko_nodes import BotkoOPCUANode
from src.communication.opcua import OPCClientProtocol
from src.communication.profiler import ProfilerProtocol
from src.detections.free_space import EmptyRectArea
from src.gui.gui import GUIProtocol
from src.product.passport import Passport


async def send_free_space(opc_client: OPCClientProtocol, free_space: EmptyRectArea) -> None:
    await opc_client.send(BotkoOPCUANode.HV_ENGRAVING_RADIUS, free_space.r)
    await opc_client.send(BotkoOPCUANode.HV_ENGRAVING_ANGLE, free_space.theta)
    await opc_client.send(BotkoOPCUANode.HV_ENGRAVING_HEIGHT, free_space.z)
    await opc_client.send(BotkoOPCUANode.HV_ENGRAVING_LOCATION_READY, True)


async def send_passport(opc_client: OPCClientProtocol, passport: Passport) -> None:
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_OWNER_NAME, passport.owner_name)
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_OWNER_CODE, passport.owner_code)
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_SERIAL_NUMBER, passport.serial_number)
    await opc_client.send(
        BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_NAME, passport.manufacturer_name
    )
    await opc_client.send(
        BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_CODE, passport.manufacturer_code
    )
    await opc_client.send(
        BotkoOPCUANode.HV_PASSPORT_MANUFACTURER_SERIAL_NUMBER,
        passport.manufacturer_serial_number,
    )
    await opc_client.send(
        BotkoOPCUANode.HV_PASSPORT_DATE_MANUFACTURING, passport.manufacturing_date
    )
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_DATE_LAST_TEST, passport.last_test_date)
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_TEST_PRESSURE, passport.test_pressure)
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_CAPACITY, passport.capacity)
    await opc_client.send(
        BotkoOPCUANode.HV_PASSPORT_ORIGINAL_TARRA_WEIGHT, passport.original_tare_weight
    )
    await opc_client.send(BotkoOPCUANode.HV_PASSPORT_READY, True)


async def initialize(
    profiler: ProfilerProtocol, opc_client: OPCClientProtocol, gui: GUIProtocol
) -> FrameQueue:
    logger.info('Initializing the main flow...')
    try:
        frame_queue = await profiler.start_run()
        logger.info('Profiler started successfully.')
        await opc_client.initialize()
        logger.info('OPC client initialized successfully.')
        await gui.initialize()
        logger.info('GUI initialized successfully.')
        return frame_queue
    except Exception as e:
        logger.error(f'Failed to properly initialize: {e}')
        raise


async def flow_iteration(
    profiler: ProfilerProtocol,
    frame_queue: FrameQueue,
    opc_client: OPCClientProtocol,
    analyzer: AnalyzerProtocol,
    gui: GUIProtocol,
) -> None:
    logger.trace('Flow iteration started.')
    try:
        length = await opc_client.receive(BotkoOPCUANode.BOTKO_LENGTH)
        diameter = await opc_client.receive(BotkoOPCUANode.BOTKO_DIAMETER)
        await gui.clear()
        profiler.update_settings(length=length, diameter=diameter)
        await opc_client.receive(BotkoOPCUANode.BOTKO_SCAN_REQUEST)
        await opc_client.send(BotkoOPCUANode.HV_SCAN_ACKNOWLEDGE, True)
        await opc_client.send(BotkoOPCUANode.HV_START_ROTATION_REQUEST, True)
        profiler.trigger()
        await opc_client.send(BotkoOPCUANode.HV_SCAN_BUSY, True)
        frame: np.ndarray = await frame_queue.get()
        await opc_client.send(BotkoOPCUANode.HV_SCAN_FINISHED, True)
        await opc_client.send(BotkoOPCUANode.HV_SCAN_BUSY, False)
        await opc_client.send(BotkoOPCUANode.HV_START_ROTATION_REQUEST, False)
        free_spaces = analyzer.get_free_space(frame)
        if len(free_spaces) == 0:
            logger.warning('No free spaces detected in the frame.')
            await opc_client.send(BotkoOPCUANode.HV_FAULTED, True)
            return
        await send_free_space(opc_client, free_spaces[0])
        passport = analyzer.get_passport_from_ocr(frame)
        await send_passport(opc_client, passport)
        await gui.new_measurement(passport, frame)

    except Exception as e:
        logger.error(f'Error during flow iteration: {e}')
        raise


async def main():
    from pathlib import Path

    from heliovision.config import config

    from src.analyzer import MockAnalyzer
    from src.communication.opcua import OPCClient
    from src.communication.profiler import MockProfiler
    from src.gui.gui import Gui

    logger.info('Starting the main application...')
    profiler = MockProfiler(data_folder=Path('./data'), delay=0.1)
    opc_client = OPCClient(
        server_url=config.get_setting('opcua', 'server_url'),
        node_descriptions=BotkoOPCUANode,
    )
    gui = Gui()
    analyzer = MockAnalyzer()
    try:
        frame_queue = await initialize(profiler, opc_client, gui)
        logger.info('Initialization completed successfully.')
        while True:
            await flow_iteration(profiler, frame_queue, opc_client, analyzer, gui)
    except Exception as e:
        logger.error(f'An error occurred in the main flow: {e}')
        await opc_client.send(BotkoOPCUANode.HV_FAULTED, True)
    finally:
        logger.info('Shutting down the application...')
        await gui.clear()
        logger.info('Application shutdown complete.')


if __name__ == '__main__':
    asyncio.run(main())

import asyncio

import pytest
import pytest_asyncio
from asyncua import Node, Server
from asyncua.ua import <PERSON>V<PERSON><PERSON>, Variant, VariantType

from src.communication.opcua import OPCClient
from src.communication.opcua_nodes import OPCUANodes, TypedNode
from src.mock.mock_opc_server import generate_nodes


@pytest_asyncio.fixture(scope='function', loop_scope='function')
async def mock_server():
    """Fixture to set up a mock OPC UA server."""
    server = Server()
    await server.init()
    server.set_endpoint('opc.tcp://localhost:4840/')
    uri = 'general_test'
    address_space = await server.register_namespace(uri)
    root = server.get_objects_node()

    # Add nodes to the server
    nodes = await generate_nodes(
        root,
        address_space,
        [
            {'node_id': ['ns=2;i=2'], 'type': 'Boolean', 'default': False},
            {'node_id': ['ns=2;i=3'], 'type': 'Int32', 'default': 0},
            {'node_id': ['ns=2;i=4'], 'type': 'String', 'default': ''},
            {
                'node_id': ['ns=2;i=5'],
                'type': 'Int32',
                'default': [0] * 5,
                'array_dimensions': [5],
            },
            {'node_id': ['ns=2;i=6'], 'type': 'Float', 'default': 0.0},
        ],
    )

    await server.start()

    await asyncio.sleep(1)

    # Start the server
    try:
        yield {
            'server': server,
            'nodes': nodes,
        }
        # yield server
    finally:
        await server.stop()


class TestOPCUANodes(OPCUANodes):
    """Test OPC UA nodes."""

    TEST_NODE_1 = TypedNode('ns=2;i=2', data_type=bool)
    TEST_NODE_2 = TypedNode('ns=2;i=3', data_type=int)
    TEST_NODE_3 = TypedNode('ns=2;i=4', data_type=str)
    TEST_NODE_4 = TypedNode('ns=2;i=5', data_type=int)
    TEST_NODE_5 = TypedNode('ns=2;i=6', data_type=float)


@pytest_asyncio.fixture(scope='function', loop_scope='function')
async def botko_client():
    """Fixture to set up the OPCClient."""

    client = OPCClient(
        server_url='opc.tcp://localhost:4840',
        node_descriptions=TestOPCUANodes,
        namespace='general_test',
    )
    await client.initialize()
    return client


@pytest.mark.asyncio
async def test_client_send_all_valid_values(mock_server, botko_client: OPCClient):
    """Test that OPCClient can send all valid values."""
    nodes = mock_server['nodes']
    test_values = {
        TestOPCUANodes.TEST_NODE_1: True,
        TestOPCUANodes.TEST_NODE_2: 42,
        TestOPCUANodes.TEST_NODE_3: 'Hello',
        TestOPCUANodes.TEST_NODE_4: [1, 2, 3, 4, 5],
        TestOPCUANodes.TEST_NODE_5: 3.14,
    }

    for node_id, value in test_values.items():
        node: Node = nodes[node_id.value.node_id]
        await botko_client.send(node_id, value)
        server_value = await node.get_value()
        assert server_value == pytest.approx(value), f'Expected {value}, but got {server_value}'


@pytest.mark.asyncio
async def test_client_send_invalid_node_id(mock_server, botko_client: OPCClient):
    """Test that OPCClient raises ValueError for invalid node IDs."""

    class InvalidNode(OPCUANodes):
        """Invalid OPC UA nodes."""

        INVALID_NODE = TypedNode('ns=2;i=999', data_type=int)

    with pytest.raises(KeyError):
        await botko_client.send(InvalidNode.INVALID_NODE, 42)


@pytest.mark.asyncio
async def test_client_send_invalid_value(mock_server, botko_client: OPCClient):
    """Test that OPCClient raises ValueError for invalid values."""
    invalid_values = {
        TestOPCUANodes.TEST_NODE_1: 42,  # Invalid type for Boolean
        TestOPCUANodes.TEST_NODE_2: 'Hello',  # Invalid type for Int32
        TestOPCUANodes.TEST_NODE_3: 3.14,  # Invalid type for String
        TestOPCUANodes.TEST_NODE_4: [1, 2, 'Hello'],  # Mixed types in array
        TestOPCUANodes.TEST_NODE_5: 'Hello',  # Invalid type for Float
    }

    for node_id, value in invalid_values.items():
        with pytest.raises(ValueError):
            await botko_client.send(node_id, value)


@pytest.mark.asyncio
async def test_client_receive_all_valid_values(mock_server, botko_client: OPCClient):
    """Test that OPCClient can receive all valid values."""
    nodes = mock_server['nodes']
    test_values = {
        TestOPCUANodes.TEST_NODE_1: {
            'value': False,
            'data_type': VariantType.Boolean,
            'is_array': False,
        },
        TestOPCUANodes.TEST_NODE_2: {
            'value': 69,
            'data_type': VariantType.Int32,
            'is_array': False,
        },
        TestOPCUANodes.TEST_NODE_3: {
            'value': 'World',
            'data_type': VariantType.String,
            'is_array': False,
        },
        TestOPCUANodes.TEST_NODE_4: {
            'value': [5, 4, 3, 2, 1],
            'data_type': VariantType.Int32,
            'is_array': True,
        },
        TestOPCUANodes.TEST_NODE_5: {
            'value': 0.73,
            'data_type': VariantType.Float,
            'is_array': False,
        },
    }

    for node_id, value in test_values.items():
        node: Node = nodes[node_id.value.node_id]
        server_value = DataValue(
            Variant(value['value'], value['data_type'], is_array=value['is_array'])
        )
        await node.set_value(server_value)
        received_value = await botko_client.receive(node_id)
        assert received_value == pytest.approx(value['value']), (
            f'Expected {value["value"]}, but got {received_value}'
        )

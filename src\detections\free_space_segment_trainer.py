from functools import partial

import cv2 as cv
import matplotlib.pyplot as plt
import numpy as np
from skimage import feature, future, segmentation
from sklearn.ensemble import RandomForestClassifier

from src.utils.labelme_to_mask import parse_labelme_to_mask

# Load image
y = 242
h = 2000
img = cv.imread(
    'data/OCR/20250415/images/images_processed/20250415_102820052292_prep.png',
    cv.IMREAD_GRAYSCALE,
)

# Load labelme labels
# Polygons only
# labels: clear, text
training_labels = parse_labelme_to_mask(
    'data/OCR/20250415/images/images_processed/20250415_102820052292_prep.json',
)

# Crop
img = img[y : y + h, :]
training_labels = training_labels[y : y + h, :]

sigma_min = 1
sigma_max = 16
features_func = partial(
    feature.multiscale_basic_features,
    intensity=True,
    edges=False,
    texture=True,
    sigma_min=sigma_min,
    sigma_max=sigma_max,
    channel_axis=-1,
)
features = features_func(img)
clf = RandomForestClassifier(n_estimators=50, n_jobs=-1, max_depth=10, max_samples=0.05)
clf = future.fit_segmenter(training_labels, features, clf)
result = future.predict_segmenter(features, clf)

fig, ax = plt.subplots(1, 2, sharex=True, sharey=True, figsize=(9, 4))
ax[0].imshow(segmentation.mark_boundaries(img, result, mode='thick'))
ax[0].contour(training_labels)
ax[0].set_title('Image, mask and segmentation boundaries')
ax[1].imshow(result)
ax[1].set_title('Segmentation')
fig.tight_layout()
plt.show()
